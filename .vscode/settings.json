{
    "git.ignoreLimitWarning": true,
    "python.linting.pylintPath": "/Users/<USER>/miniconda3/bin/pylint",
    "python.linting.pylintArgs": [
        // https://pylint.readthedocs.io/en/latest/technical_reference/features.html
        "--disable",
        "no-member, arguments-differ, unused-argument, attribute-defined-outside-init, broad-except, missing-docstring, invalid-name, no-self-use, too-few-public-methods, too-many-ancestors, too-many-instance-attributes, too-many-arguments, line-too-long, protected-access",
        "--init-hook",
        "import sys; sys.path.extend(['${workspaceFolder}/venv/Lib/site-packages',]);"
    ],
    "python.languageServer": "None",
}