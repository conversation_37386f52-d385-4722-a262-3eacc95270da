# 图像风格转换模块

这个模块使用可灵AI的图生图API对图像进行风格转换。它可以将普通图像转换为具有特定艺术风格的图像，如油画风格、水彩画风格等。

## 功能特点

- 支持单张图像处理和批量处理
- 提供命令行工具和Web界面两种使用方式
- 支持自定义风格描述和负向提示词
- 支持将处理结果上传到阿里云OSS

## 使用前提

使用此模块前，需要设置以下环境变量：

```bash
# 可灵AI API密钥
export KELIN_API_KEY="your_kelin_api_key"

# 阿里云OSS访问凭证（如需上传功能）
export OSS_ACCESS_KEY_ID="your_oss_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_oss_access_key_secret"
```

## 使用方法

### 1. 通过Web界面使用

运行独立的Streamlit应用：

```bash
cd /path/to/auto_video_extract
streamlit run style_app.py
```

或者在主应用中使用：

```python
from code.image_style_trans.style_ui import StyleUI

# 创建并渲染UI
style_ui = StyleUI(project_root, output_dir)
style_ui.render()
```

### 2. 通过命令行工具使用

处理单张图像：

```bash
python -m code.tools.style_transfer_tool single \
    --image /path/to/image.jpg \
    --prompt "油画风格" \
    --negative "模糊，扭曲" \
    --output-dir /path/to/output
```

批量处理目录中的图像：

```bash
python -m code.tools.style_transfer_tool directory \
    --directory /path/to/images \
    --prompt "水彩画风格" \
    --negative "模糊，扭曲" \
    --output-dir /path/to/output
```

### 3. 在代码中使用

```python
from code.image_style_trans.style_processor import StyleProcessor

# 初始化风格处理器
processor = StyleProcessor(api_key="your_kelin_api_key")

# 处理单张图像
result_path = processor.process_image(
    image_path="/path/to/image.jpg",
    prompt="油画风格",
    negative_prompt="模糊，扭曲",
    output_dir="/path/to/output"
)

# 批量处理图像
image_paths = ["/path/to/image1.jpg", "/path/to/image2.jpg"]
successful_paths = processor.process_batch(
    image_paths=image_paths,
    prompt="水彩画风格",
    negative_prompt="模糊，扭曲",
    output_dir="/path/to/output"
)

# 处理并上传到OSS
oss_url = processor.process_and_upload(
    image_path="/path/to/image.jpg",
    prompt="素描风格",
    negative_prompt="模糊，扭曲",
    output_dir="/path/to/output",
    oss_path="styled_images/result.jpg"
)
```

## 风格提示词示例

以下是一些风格提示词示例，可以用于生成不同风格的图像：

- 油画风格："oil painting style, masterpiece, detailed brushstrokes"
- 水彩画风格："watercolor painting, soft colors, flowing, artistic"
- 素描风格："pencil sketch, detailed lines, monochrome, artistic"
- 梵高风格："Van Gogh style, swirling patterns, bold colors, expressive"
- 赛博朋克风格："cyberpunk style, neon lights, futuristic, high tech"
- 卡通风格："cartoon style, vibrant colors, simple shapes, cute"
- 日式动漫风格："anime style, Japanese animation, colorful, detailed"
- 像素艺术风格："pixel art style, retro gaming, 8-bit, blocky"

## 负向提示词示例

以下是一些负向提示词示例，可以用于避免生成不希望的特征：

- 常见问题："blurry, distorted, low quality, low resolution, bad anatomy"
- 过度细节："too many details, overly complex, cluttered"
- 不自然色彩："oversaturated, unnatural colors, harsh lighting"

## 注意事项

- 图像处理可能需要一些时间，请耐心等待
- 处理结果的质量取决于输入图像的质量和提示词的描述
- 可灵AI API可能有使用限制和费用，请参考官方文档