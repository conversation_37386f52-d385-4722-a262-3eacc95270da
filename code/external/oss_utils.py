import oss2
import os
from pathlib import Path
import logging

class OssManager:
    def __init__(self, access_key_id=None, access_key_secret=None, endpoint=None, bucket_name=None):
        """
        初始化OSS管理器
        :param access_key_id: 阿里云AccessKeyId，如果为None则从环境变量获取
        :param access_key_secret: 阿里云AccessKeySecret，如果为None则从环境变量获取
        :param endpoint: OSS访问域名，如果为None则使用默认值
        :param bucket_name: 存储空间名称，如果为None则使用默认值
        """
        # 从环境变量或参数获取访问凭证
        self.access_key_id = access_key_id or os.environ.get('OSS_ACCESS_KEY_ID', "LTAI5tJv7vshL6BPD52BJsUg")
        self.access_key_secret = access_key_secret or os.environ.get('OSS_ACCESS_KEY_SECRET', "******************************")

        # 设置默认值
        self.bucket_name = bucket_name or "public-audio-xiaolongtuan"
        self.endpoint = endpoint or 'oss-cn-beijing.aliyuncs.com'

        # 验证必要参数
        if not self.access_key_id or not self.access_key_secret:
            logging.warning("OSS访问凭证未设置，OSS功能将不可用。请设置OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET环境变量。")
            self.available = False
            return

        # 初始化OSS客户端
        try:
            # 使用oss2初始化认证和客户端
            auth = oss2.Auth(self.access_key_id, self.access_key_secret)
            self.bucket = oss2.Bucket(auth, self.endpoint, self.bucket_name)
            self.available = True
            logging.info(f"OSS客户端初始化成功，Bucket: {self.bucket_name}")
        except Exception as e:
            logging.error(f"OSS客户端初始化失败: {str(e)}")
            self.available = False
    
    def upload_file(self, local_file_path, oss_path=None):
        """
        上传文件到OSS
        :param local_file_path: 本地文件路径
        :param oss_path: OSS中的存储路径，如果为None则使用文件名
        :return: 上传成功返回文件的URL，失败返回None
        """
        if not self.available:
            logging.warning("OSS客户端未正确初始化，无法上传文件")
            return None

        try:
            local_path = Path(local_file_path)
            if not local_path.exists():
                logging.error(f"要上传的文件不存在: {local_file_path}")
                return None

            # 如果未指定OSS路径，则使用文件名
            if oss_path is None:
                oss_path = local_path.name

            # 上传文件
            result = self.bucket.put_object_from_file(oss_path, local_file_path)

            if result.status == 200:
                # 生成正确的文件URL格式
                file_url = f"https://{self.bucket_name}.{self.endpoint}/{oss_path}"
                logging.info(f"文件上传成功: {file_url}")
                return file_url
            else:
                logging.error(f"文件上传失败，状态码: {result.status}")
                return None
        except Exception as e:
            logging.error(f"上传文件到OSS时发生错误: {str(e)}")
            return None
    
    def upload_directory(self, local_dir_path, oss_prefix=None):
        """
        上传目录下的所有文件到OSS
        :param local_dir_path: 本地目录路径
        :param oss_prefix: OSS中的前缀路径
        :return: 上传成功的文件URL字典，key为本地路径，value为OSS URL
        """
        if not self.available:
            logging.warning("OSS客户端未正确初始化，无法上传文件")
            return {}
            
        try:
            local_dir = Path(local_dir_path)
            if not local_dir.exists() or not local_dir.is_dir():
                logging.error(f"要上传的目录不存在: {local_dir_path}")
                return {}
                
            # 如果未指定OSS前缀，则使用目录名
            if oss_prefix is None:
                oss_prefix = local_dir.name
                
            # 确保前缀以/结尾
            if not oss_prefix.endswith('/'):
                oss_prefix += '/'
                
            # 上传目录下的所有文件
            result_urls = {}
            for file_path in local_dir.glob('**/*'):
                if file_path.is_file():
                    # 计算相对路径
                    rel_path = file_path.relative_to(local_dir)
                    oss_path = f"{oss_prefix}{rel_path}"
                    
                    # 上传文件
                    url = self.upload_file(str(file_path), oss_path)
                    if url:
                        result_urls[str(file_path)] = url
            
            return result_urls
        except Exception as e:
            logging.error(f"上传目录到OSS时发生错误: {str(e)}")
            return {}
    
    def get_file_url(self, oss_path):
        """
        获取OSS文件的URL
        :param oss_path: OSS中的存储路径
        :return: 文件的URL
        """
        if not self.available:
            return None

        try:
            # 检查文件是否存在
            if self.bucket.object_exists(oss_path):
                # 生成文件URL
                file_url = f"https://{self.bucket_name}.{self.endpoint}/{oss_path}"
                return file_url
            else:
                logging.error(f"OSS中不存在该文件: {oss_path}")
                return None
        except Exception as e:
            logging.error(f"获取OSS文件URL时发生错误: {str(e)}")
            return None
    
    def delete_file(self, oss_path):
        """
        从OSS删除文件
        :param oss_path: OSS中的存储路径
        :return: 是否成功删除
        """
        if not self.available:
            logging.warning("OSS客户端未正确初始化，无法删除文件")
            return False

        try:
            # 删除文件
            result = self.bucket.delete_object(oss_path)

            if result.status == 204:
                logging.info(f"文件删除成功: {oss_path}")
                return True
            else:
                logging.error(f"文件删除失败，状态码: {result.status}")
                return False
        except Exception as e:
            logging.error(f"删除OSS文件时发生错误: {str(e)}")
            return False