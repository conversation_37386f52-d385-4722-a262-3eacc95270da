import os
import json
import base64
import requests
import logging
import time
import jwt
from pathlib import Path

class KelinAIService:
    def __init__(self, api_key=None, secret_key=None):
        """
        初始化可灵AI服务客户端
        :param api_key: 可灵AI Access Key，如果为None则从环境变量获取
        :param secret_key: 可灵AI Secret Key，如果为None则从环境变量获取
        """
        # 从环境变量或参数获取API密钥
        self.access_key = api_key or os.environ.get('KELIN_ACCESS_KEY')
        print(self.access_key)
        self.secret_key = secret_key or os.environ.get('KELIN_SECRET_KEY')
        print(self.secret_key)

        # 验证必要参数
        if not self.access_key or not self.secret_key:
            logging.warning("可灵AI Access Key或Secret Key未设置，图像生成功能将不可用。请设置KELIN_ACCESS_KEY和KELIN_SECRET_KEY环境变量。")
            self.available = False
            return
            
        # API端点
        self.base_url = "https://api-beijing.klingai.com"
        self.image_generation_url = f"{self.base_url}/v1/images/generations"
        self.available = True
        logging.info("可灵AI服务客户端初始化成功")
    
    def generate_jwt_token(self):
        """
        生成JWT令牌
        :return: JWT令牌字符串
        """
        # 确保使用正确的header格式
        headers = {
            "alg": "HS256",
            "typ": "JWT"
        }
        
        # 确保payload包含所有必要字段
        current_time = int(time.time())
        payload = {
            "iss": self.access_key,  # 发行者必须是access_key
            "exp": current_time + 1800,  # 有效期30分钟
            "nbf": current_time - 5  # 生效时间为当前时间减5秒
        }
        
        # 使用PyJWT库生成令牌，确保使用HS256算法
        try:
            # 注意：某些PyJWT版本可能需要不同的参数顺序
            token = jwt.encode(payload=payload, key=self.secret_key, algorithm="HS256", headers=headers)
            
            # PyJWT 2.0.0以上版本返回字符串，1.7.1及以下版本返回bytes
            if isinstance(token, bytes):
                token = token.decode('utf-8')
                
            return token
        except Exception as e:
            logging.error(f"生成JWT令牌时出错: {str(e)}")
            return None
    
    def get_auth_header(self):
        """
        获取包含Authorization的请求头
        :return: 请求头字典
        """
        token = self.generate_jwt_token()
        if not token:
            logging.error("无法生成有效的JWT令牌")
            return {"Content-Type": "application/json"}
            
        # 确保Authorization格式正确：Bearer后面有一个空格，然后是token
        return {
            "Content-Type": "application/json",
            "Authorization": "Bearer "+ token
        }
    
    def generate_image(self, prompt, negative_prompt=None, model_name="kling-v2", n=1, aspect_ratio="1:1"):
        """
        使用可灵AI生成图像
        :param prompt: 正向文本提示词
        :param negative_prompt: 负向文本提示词
        :param model_name: 模型名称，默认为kling-v2
        :param n: 生成图片数量，默认为1
        :param aspect_ratio: 生成图片的画面纵横比，默认为1:1
        :return: 成功时返回任务ID，失败时返回None
        """
        if not self.available:
            logging.warning("可灵AI服务客户端未正确初始化，无法生成图像")
            return None
            
        try:
            # 获取带有JWT令牌的请求头
            headers = self.get_auth_header()
            
            # 构建请求体
            payload = {
                "model_name": model_name,
                "prompt": prompt,
                "n": n,
                "aspect_ratio": aspect_ratio
            }
            
            # 添加可选参数
            if negative_prompt:
                payload["negative_prompt"] = negative_prompt
            
            # 发送请求
            response = requests.post(self.image_generation_url, headers=headers, json=payload)
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0 and "data" in result:
                    task_id = result["data"]["task_id"]
                    logging.info(f"图像生成任务提交成功，任务ID: {task_id}")
                    return task_id
                else:
                    logging.error(f"图像生成任务提交失败: {result.get('message')}")
                    return None
            else:
                logging.error(f"图像生成请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logging.error(f"生成图像时发生错误: {str(e)}")
            return None
    
    def get_task_status(self, task_id):
        """
        获取图像生成任务状态
        :param task_id: 任务ID
        :return: 任务状态信息字典，失败时返回None
        """
        if not self.available:
            logging.warning("可灵AI服务客户端未正确初始化，无法获取任务状态")
            return None
            
        try:
            # 获取带有JWT令牌的请求头
            headers = self.get_auth_header()
            
            # 构建请求URL
            url = f"{self.base_url}/v1/images/generations/{task_id}"
            
            # 发送请求
            response = requests.get(url, headers=headers)
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0 and "data" in result:
                    logging.info(f"获取任务状态成功: {result['data']['task_status']}")
                    return result["data"]
                else:
                    logging.error(f"获取任务状态失败: {result.get('message')}")
                    return None
            else:
                logging.error(f"获取任务状态请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logging.error(f"获取任务状态时发生错误: {str(e)}")
            return None
    
    def wait_for_task_completion(self, task_id, max_wait_time=300, check_interval=5):
        """
        等待图像生成任务完成
        :param task_id: 任务ID
        :param max_wait_time: 最大等待时间（秒），默认为300秒
        :param check_interval: 检查间隔（秒），默认为5秒
        :return: 成功时返回图像URL列表，失败时返回None
        """
        if not self.available:
            logging.warning("可灵AI服务客户端未正确初始化，无法等待任务完成")
            return None
            
        try:
            start_time = time.time()
            while time.time() - start_time < max_wait_time:
                # 获取任务状态
                task_info = self.get_task_status(task_id)
                if not task_info:
                    logging.error("获取任务状态失败")
                    return None
                    
                # 检查任务状态
                status = task_info.get("task_status")
                if status == "succeed":
                    # 任务成功完成
                    if "task_result" in task_info and "images" in task_info["task_result"]:
                        image_urls = [img["url"] for img in task_info["task_result"]["images"]]
                        logging.info(f"任务完成，获取到{len(image_urls)}个图像URL")
                        return image_urls
                    else:
                        logging.error("任务完成但未找到图像URL")
                        return None
                elif status == "failed":
                    # 任务失败
                    logging.error(f"任务失败: {task_info.get('task_status_msg')}")
                    return None
                elif status in ["submitted", "processing"]:
                    # 任务仍在处理中，等待后继续检查
                    logging.info(f"任务状态: {status}，继续等待...")
                    time.sleep(check_interval)
                else:
                    # 未知状态
                    logging.warning(f"未知任务状态: {status}")
                    time.sleep(check_interval)
            
            # 超时
            logging.error(f"等待任务完成超时（{max_wait_time}秒）")
            return None
        except Exception as e:
            logging.error(f"等待任务完成时发生错误: {str(e)}")
            return None
    
    def image_style_transfer(self, image_path, prompt, negative_prompt=None, model_name="kling-v2"):
        """
        图像风格转换
        :param image_path: 本地图像文件路径
        :param prompt: 风格描述提示词
        :param negative_prompt: 负向提示词
        :param model_name: 模型名称，默认为kling-v2
        :return: 成功时返回转换后的图像URL，失败时返回None
        """
        if not self.available:
            logging.warning("可灵AI服务客户端未正确初始化，无法进行图像风格转换")
            return None
            
        try:
            # 检查文件是否存在
            image_path = Path(image_path)
            if not image_path.exists():
                logging.error(f"图像文件不存在: {image_path}")
                return None
                
            # 读取图像文件并进行Base64编码
            with open(image_path, "rb") as f:
                image_data = f.read()
                image_base64 = base64.b64encode(image_data).decode("utf-8")
            
            # 获取带有JWT令牌的请求头
            headers = self.get_auth_header()
            
            # 构建请求体
            payload = {
                "model_name": model_name,
                "prompt": prompt,
                "image": image_base64,
                "n": 1,
            }
            
            # 添加可选参数
            if negative_prompt:
                payload["negative_prompt"] = negative_prompt
            
            # 发送请求
            response = requests.post(self.image_generation_url, headers=headers, json=payload)
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0 and "data" in result:
                    task_id = result["data"]["task_id"]
                    logging.info(f"图像风格转换任务提交成功，任务ID: {task_id}")
                    
                    # 等待任务完成并获取结果
                    image_urls = self.wait_for_task_completion(task_id)
                    if image_urls and len(image_urls) > 0:
                        return image_urls[0]  # 返回第一个图像URL
                    else:
                        logging.error("等待任务完成失败或未获取到图像URL")
                        return None
                else:
                    logging.error(f"图像风格转换任务提交失败: {result.get('message')}")
                    return None
            else:
                logging.error(f"图像风格转换请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logging.error(f"图像风格转换时发生错误: {str(e)}")
            return None