import json
import time
import os
import pandas as pd
import datetime
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from aliyunsdkcore.auth.credentials import AccessKeyCredential

class AliyunASR:
    def __init__(self, access_key_id=None, access_key_secret=None, app_key=None):
        """
        初始化阿里云语音识别客户端（使用通义听悟API）
        """
        # 从环境变量或参数获取访问凭证
        self.access_key_id = access_key_id or os.environ.get('ALIYUN_AK_ID', "LTAI5tJv7vshL6BPD52BJsUg")
        self.access_key_secret = access_key_secret or os.environ.get('ALIYUN_AK_SECRET', "******************************")
        self.app_key = app_key or os.environ.get('NLS_APP_KEY', "7g7VG0bhOzKNUvEC")
        
        # 验证必要参数
        if not self.access_key_id or not self.access_key_secret or not self.app_key:
            print("阿里云语音识别凭证未设置，请设置ALIYUN_AK_ID、ALIYUN_AK_SECRET和NLS_APP_KEY环境变量。")
            self.available = False
        else:
            self.available = True
            
        # 设置听悟API参数
        self.DOMAIN = "tingwu.cn-beijing.aliyuncs.com"
        self.API_VERSION = "2023-09-30"
        self.PROTOCOL_TYPE = "https"
        self.CREATE_TASK_URI = "/openapi/tingwu/v2/tasks"
        self.GET_TASK_INFO_URI = "/openapi/tingwu/v2/tasks/{TaskId}"
        
        # 创建AcsClient实例（如果凭证可用）
        if self.available:
            credentials = AccessKeyCredential(self.access_key_id, self.access_key_secret)
            self.client = AcsClient(region_id='cn-beijing', credential=credentials)
    
    def transcribe_file(self, file_url, output_csv_path):
        """
        使用阿里云通义听悟服务转录音频文件
        :param file_url: 音频文件URL（必须是公开可访问的URL）
        :param output_csv_path: 输出CSV文件路径
        :return: 成功时返回(True, task_id)，失败时返回(False, None)
        """
        if not self.available:
            print("阿里云语音识别凭证未正确设置，无法使用转录服务")
            return False, None
            
        try:
            # 提交录音文件识别请求
            print("提交阿里云通义听悟识别请求...")
            task_id = self._submit_task(file_url)
            if not task_id:
                print("提交识别任务失败")
                return False, None
                
            print(f"任务提交成功，任务ID: {task_id}")
            
            # 轮询获取识别结果
            print("等待识别结果...")
            result = self._get_task_result(task_id)
            if not result:
                print("获取识别结果失败")
                return False, task_id
                
            # 解析结果并保存为CSV
            print("解析识别结果并保存为CSV...")
            success = self._parse_result_to_csv(result, output_csv_path)
            return success, task_id
            
        except Exception as e:
            print(f"转录过程中出现错误: {e}")
            return False, None
    
    def _create_common_request(self, uri, method):
        """
        创建通用请求对象
        :param uri: 请求URI
        :param method: 请求方法
        :return: CommonRequest对象
        """
        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain(self.DOMAIN)
        request.set_version(self.API_VERSION)
        request.set_protocol_type(self.PROTOCOL_TYPE)
        request.set_method(method)
        request.set_uri_pattern(uri)
        request.add_header('Content-Type', 'application/json')
        return request
    
    def _submit_task(self, file_url):
        """
        提交录音文件识别请求
        :param file_url: 音频文件URL
        :return: 任务ID或None（如果失败）
        """
        # 创建请求
        request = self._create_common_request(self.CREATE_TASK_URI, 'PUT')
        request.add_query_param('type', 'offline')
        
        # 设置任务参数
        body = {}
        body['AppKey'] = self.app_key
        
        # 基本请求参数
        input_params = {}
        input_params['SourceLanguage'] = 'en'
        input_params['TaskKey'] = 'task' + datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        input_params['FileUrl'] = file_url
        body['Input'] = input_params
        
        # AI相关参数
        parameters = {}
        
        # 语音识别控制相关
        transcription = {}
        # 角色分离
        transcription['DiarizationEnabled'] = False
        diarization = {}
        transcription['Diarization'] = diarization
        parameters['Transcription'] = transcription
        
        body['Parameters'] = parameters
        
        # 设置请求内容
        request.set_content(json.dumps(body).encode('utf-8'))
        
        try:
            # 发送请求
            response = self.client.do_action_with_exception(request)
            response_json = json.loads(response)
            
            # 检查响应状态
            if response_json.get("Code") == "0":
                return response_json.get("Data", {}).get("TaskId")
            else:
                print(f"提交任务失败，错误码: {response_json.get('Code')}, 消息: {response_json.get('Message')}")
                return None
        except Exception as e:
            print(f"提交任务异常: {e}")
            return None
    
    def _get_task_result(self, task_id):
        """
        获取识别结果
        :param task_id: 任务ID
        :return: 识别结果或None（如果失败）
        """
        # 创建请求
        uri = self.GET_TASK_INFO_URI.format(TaskId=task_id)
        request = self._create_common_request(uri, 'GET')
        
        # 轮询获取结果
        while True:
            try:
                # 发送请求
                response = self.client.do_action_with_exception(request)
                response_json = json.loads(response)
                
                # 检查响应状态
                if response_json.get("Code") != "0":
                    print(f"获取结果失败，错误码: {response_json.get('Code')}, 消息: {response_json.get('Message')}")
                    return None
                
                data = response_json.get("Data", {})
                task_status = data.get("TaskStatus")
                
                if task_status == "ONGOING":
                    # 继续轮询
                    print(f"任务状态: {task_status}，等待10秒后重试...")
                    time.sleep(10)
                elif task_status == "COMPLETED":
                    # 成功获取结果
                    # 获取转写结果URL
                    transcription_url = data.get("Result", {}).get("Transcription")
                    if not transcription_url:
                        print("未找到转写结果URL")
                        return None
                    
                    # 下载转写结果
                    try:
                        import requests
                        result_response = requests.get(transcription_url)
                        result_response.raise_for_status()
                        return result_response.text
                    except Exception as e:
                        print(f"下载转写结果异常: {e}")
                        return None
                else:
                    # 其他状态视为失败
                    print(f"获取结果失败，任务状态: {task_status}")
                    return None
            except Exception as e:
                print(f"获取结果异常: {e}")
                return None

    def _parse_result_to_csv(self, result_json, output_csv_path):
        """
        解析识别结果并保存为CSV
        :param result_json: 识别结果JSON字符串
        :param output_csv_path: 输出CSV文件路径
        :return: 是否成功保存
        """
        try:
            # 解析JSON结果
            result = json.loads(result_json)
            transcription = result.get('Transcription', {})

            # 检查是否有段落数据和音频片段数据
            paragraphs = transcription.get('Paragraphs', [])
            audio_segments = transcription.get('AudioSegments', [])

            if not paragraphs:
                print("识别结果中没有段落数据")
                return False

            # 准备数据
            data = []

            # 创建一个时间范围到文本的映射
            # 首先，从paragraphs中提取所有单词及其时间信息
            all_words = []
            for paragraph in paragraphs:
                words = paragraph.get('Words', [])
                for word in words:
                    all_words.append({
                        'start': word.get('Start', 0),
                        'end': word.get('End', 0),
                        'text': word.get('Text', '')
                    })

            # 按开始时间排序
            all_words.sort(key=lambda x: x['start'])

            # 使用audio_segments生成字幕
            if audio_segments:
                for segment in audio_segments:
                    if len(segment) >= 2:  # 确保segment至少有开始和结束时间
                        start_time = segment[0]
                        end_time = segment[1]

                        # 查找此时间范围内的所有单词
                        segment_words = []
                        for word in all_words:
                            # 如果单词在segment时间范围内或有重叠
                            if (word['start'] >= start_time and word['end'] <= end_time) or \
                               (word['start'] <= end_time and word['end'] >= start_time):
                                segment_words.append(word['text'])

                        # 将单词组合成文本
                        segment_text = ' '.join(segment_words).strip()

                        # 如果没有找到文本，尝试找最接近的单词
                        if not segment_text and all_words:
                            # 计算segment中点
                            segment_mid = (start_time + end_time) / 2
                            # 找到最接近的单词
                            closest_word = min(all_words, key=lambda x: abs((x['start'] + x['end'])/2 - segment_mid))
                            segment_text = closest_word['text']

                        # 格式化时间戳（转换为秒）
                        start_formatted = self._format_time(start_time / 1000)
                        end_formatted = self._format_time(end_time / 1000)
                        timestamp = f"{start_formatted}-->{end_formatted}"

                        # 添加到数据列表
                        data.append({'timestamp': timestamp, 'sentence': segment_text})
            else:
                # 如果没有audio_segments，使用paragraphs中的单词生成字幕
                # 按句子ID分组
                sentences = {}
                for paragraph in paragraphs:
                    words = paragraph.get('Words', [])
                    for word in words:
                        sentence_id = word.get('SentenceId', 0)
                        if sentence_id not in sentences:
                            sentences[sentence_id] = {
                                'start': word.get('Start', 0),
                                'end': word.get('End', 0),
                                'words': [word.get('Text', '')]
                            }
                        else:
                            sentences[sentence_id]['words'].append(word.get('Text', ''))
                            sentences[sentence_id]['start'] = min(sentences[sentence_id]['start'], word.get('Start', 0))
                            sentences[sentence_id]['end'] = max(sentences[sentence_id]['end'], word.get('End', 0))

                # 生成字幕
                for sentence_id, sentence in sentences.items():
                    start_time = sentence['start'] / 1000  # 转换为秒
                    end_time = sentence['end'] / 1000      # 转换为秒
                    text = ' '.join(sentence['words']).strip()

                    # 格式化时间戳
                    start_formatted = self._format_time(start_time)
                    end_formatted = self._format_time(end_time)
                    timestamp = f"{start_formatted}-->{end_formatted}"

                    # 添加到数据列表
                    data.append({'timestamp': timestamp, 'sentence': text})

            # 转换为DataFrame并保存
            df = pd.DataFrame(data)
            df.to_csv(output_csv_path, index=False, encoding='utf-8')
            print(f"成功将识别结果保存到 {output_csv_path}，共 {len(data)} 条记录")
            return True

        except Exception as e:
            print(f"解析结果并保存CSV时出错: {e}")
            return False

    def _format_time(self, seconds):
        """
        将秒数格式化为时间戳字符串 (HH:MM:SS,mmm)
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds_remainder = seconds % 60
        milliseconds = int((seconds_remainder - int(seconds_remainder)) * 1000)
        return f"{hours:02d}:{minutes:02d}:{int(seconds_remainder):02d},{milliseconds:03d}"