import os
import json
import logging
from openai import OpenAI
from pathlib import Path

class LlmService:
    def __init__(self, model_name="qwen-turbo", api_key=None):
        """
        :param model_name: 使用的模型名称
        :param api_key: API密钥，如果为None则从环境变量获取
        """
        self.model_name = model_name
        # 从环境变量或参数获取API密钥
        self.api_key = api_key or os.environ.get('WUJIE_ALIYUN_API_KEY')
        
        # 验证必要参数
        if not self.api_key:
            logging.warning("API Key未设置，翻译功能将不可用。请设置WUJIE_ALIYUN_API_KEY环境变量。")
            self.available = False
            return
        
        try:    
            # 初始化OpenAI客户端
            self.client = OpenAI(
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", 
                api_key=self.api_key
            )
            self.available = True
        except Exception as e:
            logging.error(f"初始化LLM客户端失败: {str(e)}")
            self.available = False
    
    def llm_chat_json(self, messages):
        """
        使用LLM进行JSON格式的聊天补全
        
        :param messages: 消息列表，包含角色和内容
        :return: 解析后的JSON响应，失败时返回None
        """
        if not self.available:
            logging.warning("LLM服务不可用，无法进行聊天补全")
            return None
            
        try:
            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                response_format={"type": "json_object"}
            )
            
            # 解析响应
            result_text = response.choices[0].message.content.strip()
            
            # 解析JSON
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                logging.error(f"无法解析LLM响应为JSON: {result_text}")
                return None
                
        except Exception as e:
            logging.error(f"LLM聊天补全时发生错误: {str(e)}")
            return None

    def translate_sentence_and_word(self, sentence, word, system_prompt=None):
        """
        翻译句子并解释单词在句子中的含义
        
        :param sentence: 英文句子
        :param word: 需要解释的单词
        :param system_prompt: 系统提示词，如果为None则使用默认提示词
        :return: 成功时返回翻译结果字典，失败时返回None
        """
        if not self.available:
            logging.warning("LLM服务不可用，无法进行翻译")
            return None
            
        try:
            # 如果没有提供系统提示词，使用默认提示词
            if not system_prompt:
                system_prompt = "我正在学习单词及其例句，请将英文例句翻译成中文，并指出该单词在例句中的中文含义。请以JSON格式返回结果，包含word_meaning和sentence_zh两个字段。"
            
            # 构建用户提示词
            user_prompt = f"word: \"{word}\"\nsentence: \"{sentence}\""
            
            # 构建消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用OpenAI API
            try:
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=messages,
                    temperature=0.3,
                    max_tokens=200,
                    response_format={"type": "json_object"}
                )
                
                # 解析响应
                result_text = response.choices[0].message.content.strip()
                
            except Exception as api_error:
                logging.error(f"调用LLM API时发生错误: {str(api_error)}")
                return None
            
            # 尝试解析JSON响应
            try:
                result_json = json.loads(result_text)
                
                # 验证响应格式
                if "word_meaning" in result_json and "sentence_zh" in result_json:
                    # 记录成功的翻译
                    logging.debug(f"成功翻译: {sentence} -> {result_json['sentence_zh']}")
                    return result_json
                else:
                    logging.error(f"翻译结果格式不正确，缺少必要字段: {result_text}")
                    return None
                    
            except json.JSONDecodeError:
                logging.error(f"无法解析翻译结果为JSON: {result_text}")
                return None
                
        except Exception as e:
            logging.error(f"翻译过程中发生未知错误: {str(e)}")
            return None