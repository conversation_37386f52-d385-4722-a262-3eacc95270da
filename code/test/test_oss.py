import os
from pathlib import Path
from external.oss_utils import OssManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_upload_audio():
    # 音频文件路径
    audio_path = "/Users/<USER>/projects/auto_video_extract/data/audio/peiqi_1.mp3"
    
    # 初始化OSS管理器
    # 注意：这里使用了硬编码的AccessKey，建议使用环境变量
    oss_manager = OssManager()
    
    if not oss_manager.available:
        logging.error("OSS客户端初始化失败，请检查访问凭证")
        return
    
    # 上传文件
    logging.info(f"开始上传文件: {audio_path}")
    oss_path = "test/peiqi_1.mp3"  # OSS中的存储路径
    file_url = oss_manager.upload_file(audio_path, oss_path)
    
    if file_url:
        logging.info(f"文件上传成功，访问URL: {file_url}")
    else:
        logging.error("文件上传失败")

if __name__ == "__main__":
    test_upload_audio()