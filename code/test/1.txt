curl --location 'https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis' \
--header 'X-DashScope-Async: enable' \
--header "Authorization: Bearer $ALIYUN_API_KEY" \
--header 'Content-Type: application/json' \
--data '{
  "model": "wanx2.1-imageedit",
  "input": {
    "function": "stylization_all",
    "prompt": "转换成法国绘本风格",
    "base_image_url": "http://*************/images/original/IP1750919559/IP1750919559_0045.jpg"
  },
  "parameters": {
    "n": 1
  }
}'


curl -X GET https://dashscope.aliyuncs.com/api/v1/tasks/eb0c1c85-707b-431e-a510-af26a82e44d4 \
     -H "Authorization: Bearer $ALIYUN_API_KEY"
