import os
import sys
import unittest
import tempfile
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service.ui_processor import convert_subtitle_to_dataframe


class TestConvertSubtitleToDataframe(unittest.TestCase):
    """测试convert_subtitle_to_dataframe函数"""

    def setUp(self):
        """测试前准备工作"""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.temp_path = Path(self.temp_dir.name)

    def tearDown(self):
        """测试后清理工作"""
        self.temp_dir.cleanup()

    def test_lrc_format(self):
        """测试LRC格式字幕文件的处理"""
        # 创建测试用LRC文件
        
        lrc_path = "/Users/<USER>/projects/auto_video_extract/data/txt/Zootopia.2016.lrc"
        # 调用函数
        df = convert_subtitle_to_dataframe(str(lrc_path))

        # 验证结果
        self.assertIsNotNone(df)
        self.assertIsInstance(df, pd.DataFrame)
        self.assertEqual(len(df), 4)  # 应该有4行数据
        self.assertTrue(all(col in df.columns for col in ["timestamp", "sentence"]))

        # 验证时间戳格式和内容
        self.assertEqual(df.iloc[0]["sentence"], "第一行文本")
        self.assertTrue("00:00:01,000-->00:00:05,500" in df.iloc[0]["timestamp"])
        
        # 验证多时间戳处理
        self.assertEqual(df.iloc[2]["sentence"], "多时间戳文本")
        self.assertTrue(len(df[df["sentence"] == "多时间戳文本"]) == 2)

    def test_srt_format(self):
        """测试SRT格式字幕文件的处理"""
        # 创建测试用SRT文件
        srt_content = (
            "1\n"
            "00:00:01,000 --> 00:00:05,000\n"
            "第一行字幕\n\n"
            "2\n"
            "00:00:06,000 --> 00:00:10,000\n"
            "第二行字幕\n"
            "第二行字幕续行\n\n"
            "3\n"
            "00:00:11,000 --> 00:00:15,000\n"
            "第三行字幕\n\n"
        )
        srt_path = self.temp_path / "test.srt"
        with open(srt_path, "w", encoding="utf-8") as f:
            f.write(srt_content)

        # 调用函数
        df = convert_subtitle_to_dataframe(str(srt_path))

        # 验证结果
        self.assertIsNotNone(df)
        self.assertIsInstance(df, pd.DataFrame)
        self.assertEqual(len(df), 3)  # 应该有3行数据
        self.assertTrue(all(col in df.columns for col in ["timestamp", "sentence"]))

        # 验证时间戳格式和内容
        self.assertEqual(df.iloc[0]["sentence"], "第一行字幕")
        self.assertEqual(df.iloc[0]["timestamp"], "00:00:01,000 --> 00:00:05,000")
        
        # 验证多行字幕合并
        self.assertEqual(df.iloc[1]["sentence"], "第二行字幕 第二行字幕续行")

    def test_unknown_encoding(self):
        """测试未知编码的字幕文件"""
        # 创建一个二进制文件模拟未知编码
        unknown_path = self.temp_path / "unknown.srt"
        with open(unknown_path, "wb") as f:
            f.write(b"\xFF\xFE\x00\x00" + b"\x00\x00\x00\x00")  # 无效的Unicode编码

        # 调用函数
        df = convert_subtitle_to_dataframe(str(unknown_path))

        # 验证结果
        self.assertIsNone(df)  # 应该返回None

    def test_invalid_file(self):
        """测试不存在的文件"""
        # 调用函数
        df = convert_subtitle_to_dataframe(str(self.temp_path / "not_exist.srt"))

        # 验证结果
        self.assertIsNone(df)  # 应该返回None

    def test_empty_file(self):
        """测试空文件"""
        # 创建空文件
        empty_path = self.temp_path / "empty.srt"
        with open(empty_path, "w") as f:
            pass

        # 调用函数
        df = convert_subtitle_to_dataframe(str(empty_path))

        # 验证结果
        self.assertIsNotNone(df)
        self.assertEqual(len(df), 0)  # 应该是空DataFrame


if __name__ == "__main__":
    unittest.main()