import os
import sys
from pathlib import Path
from service.video_processor import VideoProcessor
from external.aliyun_asr import AliyunASR
from external.oss_utils import OssManager

def process_video(video_path):
    """完整的视频处理流程测试函数"""
    print(f"开始处理视频: {video_path}")
    
    # 初始化OSS管理器
    oss_manager = OssManager()
    if not oss_manager.available:
        print("OSS服务不可用，请检查凭证设置")
        return False
    
    # 初始化阿里云ASR
    asr = AliyunASR()
    if not asr.available:
        print("阿里云ASR服务不可用，请检查凭证设置")
        return False
    
    # 初始化视频处理器
    processor = VideoProcessor()
    
    # 设置输出目录
    video_path_obj = Path(video_path)
    output_dir = video_path_obj.parent.parent / "output" / video_path_obj.stem
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置子目录
    audio_dir = output_dir / "audio"
    txt_dir = output_dir / "txt"
    frames_dir = output_dir / "frames"
    
    os.makedirs(audio_dir, exist_ok=True)
    os.makedirs(txt_dir, exist_ok=True)
    os.makedirs(frames_dir, exist_ok=True)
    
    # 设置文件路径
    audio_path = audio_dir / f"{video_path_obj.stem}.mp3"
    csv_path = txt_dir / f"{video_path_obj.stem}.csv"
    frames_output_dir = frames_dir / video_path_obj.stem
    
    # 1. 提取音频
    print("\n1. 开始提取音频...")
    if not processor.extract_audio(str(video_path), str(audio_path)):
        print("音频提取失败")
        return False
    print(f"音频提取成功: {audio_path}")
    
    # 2. 上传音频到OSS
    print("\n2. 上传音频到OSS...")
    oss_path = f"audio/{video_path_obj.stem}.mp3"
    file_url = oss_manager.upload_file(str(audio_path), oss_path)
    if not file_url:
        print("上传音频文件到OSS失败")
        return False
    print(f"音频上传成功: {file_url}")
    
    # 3. 使用阿里云ASR转录
    print("\n3. 使用阿里云通义听悟转录音频...")
    success, task_id = asr.transcribe_file(file_url, str(csv_path))
    if not success:
        print("音频转录失败")
        return False
    print(f"音频转录成功，任务ID: {task_id}")
    print(f"转录结果已保存到: {csv_path}")
    
    # 4. 提取帧
    print("\n4. 根据字幕提取视频帧...")
    if not processor.extract_frames(str(video_path), str(csv_path), str(frames_output_dir)):
        print("帧提取失败")
        return False
    print(f"帧提取成功，保存到: {frames_output_dir}")
    
    # 5. 上传处理结果到OSS
    print("\n5. 上传处理结果到OSS...")
    oss_prefix = f"processed/{video_path_obj.stem}/"
    result_urls = oss_manager.upload_directory(str(output_dir), oss_prefix)
    if not result_urls:
        print("上传处理结果到OSS失败")
    else:
        print(f"处理结果上传成功，共{len(result_urls)}个文件")
    
    print(f"\n视频处理完成! 所有输出文件保存在: {output_dir}")
    return True

if __name__ == "__main__":
    # 设置要处理的视频文件路径
    video_file = "../data/video/peiqi_1.mp4"
    
    # 检查文件是否存在
    if not os.path.exists(video_file):
        print(f"错误: 视频文件不存在: {video_file}")
        sys.exit(1)
    
    # 处理视频
    success = process_video(video_file)
    
    if success:
        print("视频处理测试成功完成!")
        sys.exit(0)
    else:
        print("视频处理测试失败!")
        sys.exit(1)