import os
import sys
from pathlib import Path

# 添加父目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 导入generate_word_dataset函数
from tools.word_dataset_generator import generate_word_dataset

def main():
    # 设置路径
    project_root = Path("/Users/<USER>/projects/auto_video_extract")
    word_list_path = project_root / "data/word_list/word_list.txt"
    ip_data_dir = project_root / "output/IP1fef9dba"
    
    # 确认文件存在
    if not word_list_path.exists():
        print(f"错误：单词列表文件不存在: {word_list_path}")
        return
    
    if not ip_data_dir.exists():
        print(f"错误：IP动画数据目录不存在: {ip_data_dir}")
        return
    
    # 打印信息
    print(f"使用单词列表: {word_list_path}")
    print(f"使用IP动画数据: {ip_data_dir}")
    print("开始生成单词数据集...")
    
    # 调用函数生成数据集
    try:
        result = generate_word_dataset(
            word_list_path=str(word_list_path),
            ip_data_dir=str(ip_data_dir)
        )
        
        if result:
            print("单词数据集生成成功！")
        else:
            print("单词数据集生成失败。")
    except Exception as e:
        print(f"生成单词数据集时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()