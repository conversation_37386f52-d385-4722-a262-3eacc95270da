import os
from external.aliyun_asr import AliyunASR
from external.oss_utils import OssManager

# 测试文件路径
audio_file = "/Users/<USER>/projects/auto_video_extract/data/audio/peiqi_1.mp3"
output_csv = "/Users/<USER>/projects/auto_video_extract/data/csv/peiqi_1_aliyun.csv"

# 初始化OSS管理器（用于上传音频文件获取URL）
oss_manager = OssManager()

# 上传音频文件到OSS获取URL
print("上传音频文件到OSS...")
oss_path = "test/peiqi_1.mp3"
file_url = oss_manager.upload_file(audio_file, oss_path)

if not file_url:
    print("上传音频文件到OSS失败，无法继续测试")
    exit(1)

print(f"音频文件已上传到: {file_url}")

# 初始化阿里云ASR
asr = AliyunASR()

# 检查ASR是否可用
if not asr.available:
    print("阿里云ASR服务不可用，请检查凭证设置")
    exit(1)

# 使用阿里云ASR转录音频
print("开始使用阿里云通义听悟转录音频...")
success, task_id = asr.transcribe_file(file_url, output_csv)
print(f"任务ID: {task_id}")

if success:
    print(f"转录成功！结果已保存到: {output_csv}")
else:
    print("转录失败，请检查日志")