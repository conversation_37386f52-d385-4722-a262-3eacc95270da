import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import argparse
from pathlib import Path
from external.kelin_service import KelinAIService

def test_image_style_transfer(image_path, prompt, negative_prompt=None, api_key=None, secret_key=None):
    """
    测试图像风格转换功能
    :param image_path: 输入图像路径
    :param prompt: 风格描述提示词
    :param negative_prompt: 负向提示词
    :param api_key: 可灵AI Access Key
    :param secret_key: 可灵AI Secret Key
    """
    # 初始化可灵AI服务
    kelin_service = KelinAIService(api_key=api_key, secret_key=secret_key)
    
    if not kelin_service.available:
        print("可灵AI服务初始化失败，请检查API密钥是否正确设置")
        return
    
    # 检查图像文件是否存在
    image_path = Path(image_path)
    if not image_path.exists():
        print(f"图像文件不存在: {image_path}")
        return
    
    print(f"正在对图像 {image_path} 进行风格转换...")
    print(f"使用提示词: {prompt}")
    if negative_prompt:
        print(f"使用负向提示词: {negative_prompt}")
    
    # 执行图像风格转换
    result_url = kelin_service.image_style_transfer(
        image_path=str(image_path),
        prompt=prompt,
        negative_prompt=negative_prompt
    )
    
    if result_url:
        print(f"图像风格转换成功，结果URL: {result_url}")
    else:
        print("图像风格转换失败")
        
def main():
    test_mode = "style"
    
    if test_mode == "style":
        # 图像风格转换测试参数
        image_path = "/Users/<USER>/projects/auto_video_extract/output/images/original/IP9754cf1e/0b0d8a26.jpg"  # 替换为实际图像路径
        prompt = "转换为蜡笔彩绘风格"  # 替换为实际提示词
        negative_prompt = "模糊，低质量"  # 可选，替换或设为None
        api_key = None  # 使用环境变量中的Access Key，或替换为实际密钥
        secret_key = None  # 使用环境变量中的Secret Key，或替换为实际密钥
        
        test_image_style_transfer(
            image_path=image_path,
            prompt=prompt,
            negative_prompt=negative_prompt,
            api_key=api_key,
            secret_key=secret_key
        )
    else:
        print("无效的测试模式，请选择 'style' 或 'generate'")

if __name__ == "__main__":
    main()