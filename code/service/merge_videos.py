import os
import argparse
from pathlib import Path

# 添加 PIL.Image.ANTIALIAS 兼容代码
try:
    import PIL.Image
    if not hasattr(PIL.Image, 'ANTIALIAS'):
        # Pillow 10.0.0+ 兼容
        PIL.Image.ANTIALIAS = PIL.Image.Resampling.LANCZOS
except (ImportError, AttributeError):
    pass

from moviepy.editor import VideoFileClip, concatenate_videoclips
from moviepy.video.fx import resize as vfx_resize  # 添加这一行导入resize效果

def merge_mp4_files(input_files, output_file, fps=None, resize_factor=None):
    """
    合并多个MP4文件为一个文件
    
    参数:
        input_files: MP4文件路径列表
        output_file: 输出文件路径
        fps: 输出视频的帧率，None表示保持原帧率
        resize_factor: 分辨率缩放因子(0-1)，None表示保持原分辨率
    
    返回:
        bool: 是否成功合并
    """
    try:
        # 检查输入文件是否存在
        for file_path in input_files:
            if not os.path.exists(file_path):
                print(f"错误: 文件不存在 - {file_path}")
                return False
        
        # 加载所有视频片段
        print(f"正在加载 {len(input_files)} 个视频文件...")
        video_clips = []
        
        for file_path in input_files:
            print(f"加载: {file_path}")
            clip = VideoFileClip(file_path)
            
            # 调整分辨率
            if resize_factor is not None and 0 < resize_factor < 1:
                original_size = clip.size
                new_width = int(original_size[0] * resize_factor)
                new_height = int(original_size[1] * resize_factor)
                print(f"调整分辨率: {original_size} -> ({new_width}, {new_height})")
                # 修改这一行，使用fx方法应用resize效果
                clip = clip.fx(vfx_resize, newsize=(new_width, new_height))
            
            video_clips.append(clip)
        
        # 合并视频片段
        print("正在合并视频...")
        final_clip = concatenate_videoclips(video_clips)
        
        # 调整帧率
        if fps is not None and fps > 0:
            original_fps = final_clip.fps
            print(f"调整帧率: {original_fps} -> {fps} fps")
            final_clip = final_clip.set_fps(fps)
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 保存合并后的视频
        print(f"正在保存合并后的视频到: {output_file}")
        final_clip.write_videofile(output_file, codec="libx264", audio_codec="aac")
        
        # 关闭所有视频片段
        for clip in video_clips:
            clip.close()
        
        print("视频合并完成!")
        return True
    except Exception as e:
        print(f"视频合并过程中出现错误: {e}")
        return False

def merge_folder_mp4_files(folder_path, output_file, sort_by='name', fps=None, resize_factor=None):
    """
    合并文件夹内的所有MP4文件
    
    参数:
        folder_path: 包含MP4文件的文件夹路径
        output_file: 输出文件路径
        sort_by: 文件排序方式，可选值：'name'(按文件名)、'time'(按修改时间)、'size'(按文件大小)
        fps: 输出视频的帧率，None表示保持原帧率
        resize_factor: 分辨率缩放因子(0-1)，None表示保持原分辨率
    
    返回:
        bool: 是否成功合并
    """
    try:
        # 检查文件夹是否存在
        if not os.path.isdir(folder_path):
            print(f"错误: 文件夹不存在 - {folder_path}")
            return False
        
        # 获取文件夹内所有MP4文件
        mp4_files = []
        for file in os.listdir(folder_path):
            if file.lower().endswith('.mp4'):
                mp4_files.append(os.path.join(folder_path, file))
        
        if not mp4_files:
            print(f"错误: 文件夹内没有MP4文件 - {folder_path}")
            return False
        
        # 根据排序方式对文件进行排序
        if sort_by == 'name':
            mp4_files.sort()
        elif sort_by == 'time':
            mp4_files.sort(key=lambda x: os.path.getmtime(x))
        elif sort_by == 'size':
            mp4_files.sort(key=lambda x: os.path.getsize(x))
        
        for file in mp4_files:
            print(f"  - {os.path.basename(file)}")
        
        # 合并文件
        return merge_mp4_files(mp4_files, output_file, fps, resize_factor)
    
    except Exception as e:
        print(f"合并文件夹内视频过程中出现错误: {e}")
        return False

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="合并MP4文件")
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 合并指定文件的命令
    files_parser = subparsers.add_parser("files", help="合并指定的多个MP4文件")
    files_parser.add_argument("-i", "--input", nargs="+", required=True, help="输入MP4文件路径列表")
    files_parser.add_argument("-o", "--output", required=True, help="输出MP4文件路径")
    files_parser.add_argument("-f", "--fps", type=float, help="输出视频的帧率，不指定则保持原帧率")
    files_parser.add_argument("-r", "--resize", type=float, help="分辨率缩放因子(0-1)，例如0.7表示缩小到原来的70%")
    
    # 合并文件夹内所有MP4文件的命令
    folder_parser = subparsers.add_parser("folder", help="合并文件夹内所有MP4文件")
    folder_parser.add_argument("-i", "--input", required=True, help="输入文件夹路径")
    folder_parser.add_argument("-o", "--output", required=True, help="输出MP4文件路径")
    folder_parser.add_argument("-s", "--sort", choices=["name", "time", "size"], default="name", 
                              help="文件排序方式: name(按文件名)、time(按修改时间)、size(按文件大小)")
    folder_parser.add_argument("-f", "--fps", type=float, help="输出视频的帧率，不指定则保持原帧率")
    folder_parser.add_argument("-r", "--resize", type=float, help="分辨率缩放因子(0-1)，例如0.7表示缩小到原来的70%")
    
    args = parser.parse_args()
    
    # 根据命令执行相应的操作
    if args.command == "files":
        merge_mp4_files(args.input, args.output, args.fps, args.resize)
    elif args.command == "folder":
        merge_folder_mp4_files(args.input, args.output, args.sort, args.fps, args.resize)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()