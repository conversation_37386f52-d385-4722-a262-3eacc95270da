<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="JSHint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="188">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="bs4" />
            <item index="2" class="java.lang.String" itemvalue="torch" />
            <item index="3" class="java.lang.String" itemvalue="numpy" />
            <item index="4" class="java.lang.String" itemvalue="networkx" />
            <item index="5" class="java.lang.String" itemvalue="matplotlib" />
            <item index="6" class="java.lang.String" itemvalue="WebOb" />
            <item index="7" class="java.lang.String" itemvalue="ryu" />
            <item index="8" class="java.lang.String" itemvalue="transformers" />
            <item index="9" class="java.lang.String" itemvalue="timm" />
            <item index="10" class="java.lang.String" itemvalue="cpm_kernels" />
            <item index="11" class="java.lang.String" itemvalue="torchvision" />
            <item index="12" class="java.lang.String" itemvalue="apex" />
            <item index="13" class="java.lang.String" itemvalue="omegaconf" />
            <item index="14" class="java.lang.String" itemvalue="pandas" />
            <item index="15" class="java.lang.String" itemvalue="tqdm" />
            <item index="16" class="java.lang.String" itemvalue="tensorflow" />
            <item index="17" class="java.lang.String" itemvalue="iopath" />
            <item index="18" class="java.lang.String" itemvalue="webdataset" />
            <item index="19" class="java.lang.String" itemvalue="accelerate" />
            <item index="20" class="java.lang.String" itemvalue="torch_geometric" />
            <item index="21" class="java.lang.String" itemvalue="cohere" />
            <item index="22" class="java.lang.String" itemvalue="llama-index-readers-file" />
            <item index="23" class="java.lang.String" itemvalue="llamaindex-py-client" />
            <item index="24" class="java.lang.String" itemvalue="llama-index-core" />
            <item index="25" class="java.lang.String" itemvalue="llama-index-legacy" />
            <item index="26" class="java.lang.String" itemvalue="llama-index-program-openai" />
            <item index="27" class="java.lang.String" itemvalue="llama-index-embeddings-openai" />
            <item index="28" class="java.lang.String" itemvalue="llama-index-agent-openai" />
            <item index="29" class="java.lang.String" itemvalue="llama-index-question-gen-openai" />
            <item index="30" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="31" class="java.lang.String" itemvalue="llama-index-multi-modal-llms-openai" />
            <item index="32" class="java.lang.String" itemvalue="llama-index" />
            <item index="33" class="java.lang.String" itemvalue="llama-index-llms-openai" />
            <item index="34" class="java.lang.String" itemvalue="openai" />
            <item index="35" class="java.lang.String" itemvalue="gensim" />
            <item index="36" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="37" class="java.lang.String" itemvalue="torch-scatter" />
            <item index="38" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="39" class="java.lang.String" itemvalue="tabulate" />
            <item index="40" class="java.lang.String" itemvalue="cython" />
            <item index="41" class="java.lang.String" itemvalue="unidecode" />
            <item index="42" class="java.lang.String" itemvalue="pickleshare" />
            <item index="43" class="java.lang.String" itemvalue="defusedxml" />
            <item index="44" class="java.lang.String" itemvalue="executing" />
            <item index="45" class="java.lang.String" itemvalue="pycparser" />
            <item index="46" class="java.lang.String" itemvalue="python-slugify" />
            <item index="47" class="java.lang.String" itemvalue="gitdb" />
            <item index="48" class="java.lang.String" itemvalue="markupsafe" />
            <item index="49" class="java.lang.String" itemvalue="cookiecutter" />
            <item index="50" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="51" class="java.lang.String" itemvalue="bleach" />
            <item index="52" class="java.lang.String" itemvalue="bottleneck" />
            <item index="53" class="java.lang.String" itemvalue="binaryornot" />
            <item index="54" class="java.lang.String" itemvalue="lxml" />
            <item index="55" class="java.lang.String" itemvalue="multiprocess" />
            <item index="56" class="java.lang.String" itemvalue="soupsieve" />
            <item index="57" class="java.lang.String" itemvalue="gitpython" />
            <item index="58" class="java.lang.String" itemvalue="jsonschema" />
            <item index="59" class="java.lang.String" itemvalue="pyre-extensions" />
            <item index="60" class="java.lang.String" itemvalue="terminado" />
            <item index="61" class="java.lang.String" itemvalue="comm" />
            <item index="62" class="java.lang.String" itemvalue="nbclassic" />
            <item index="63" class="java.lang.String" itemvalue="portalocker" />
            <item index="64" class="java.lang.String" itemvalue="pydantic" />
            <item index="65" class="java.lang.String" itemvalue="torch-cluster" />
            <item index="66" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="67" class="java.lang.String" itemvalue="pexpect" />
            <item index="68" class="java.lang.String" itemvalue="loguru" />
            <item index="69" class="java.lang.String" itemvalue="click" />
            <item index="70" class="java.lang.String" itemvalue="nbconvert" />
            <item index="71" class="java.lang.String" itemvalue="attrs" />
            <item index="72" class="java.lang.String" itemvalue="psutil" />
            <item index="73" class="java.lang.String" itemvalue="pyyaml" />
            <item index="74" class="java.lang.String" itemvalue="jedi" />
            <item index="75" class="java.lang.String" itemvalue="pure-eval" />
            <item index="76" class="java.lang.String" itemvalue="asttokens" />
            <item index="77" class="java.lang.String" itemvalue="platformdirs" />
            <item index="78" class="java.lang.String" itemvalue="jinja2-time" />
            <item index="79" class="java.lang.String" itemvalue="idna" />
            <item index="80" class="java.lang.String" itemvalue="decorator" />
            <item index="81" class="java.lang.String" itemvalue="brotlipy" />
            <item index="82" class="java.lang.String" itemvalue="pyg-lib" />
            <item index="83" class="java.lang.String" itemvalue="smmap" />
            <item index="84" class="java.lang.String" itemvalue="cffi" />
            <item index="85" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="86" class="java.lang.String" itemvalue="torch-sparse" />
            <item index="87" class="java.lang.String" itemvalue="datasets" />
            <item index="88" class="java.lang.String" itemvalue="sniffio" />
            <item index="89" class="java.lang.String" itemvalue="pyrsistent" />
            <item index="90" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="91" class="java.lang.String" itemvalue="stack-data" />
            <item index="92" class="java.lang.String" itemvalue="send2trash" />
            <item index="93" class="java.lang.String" itemvalue="zipp" />
            <item index="94" class="java.lang.String" itemvalue="nest-asyncio" />
            <item index="95" class="java.lang.String" itemvalue="smart-open" />
            <item index="96" class="java.lang.String" itemvalue="pygments" />
            <item index="97" class="java.lang.String" itemvalue="pyarrow" />
            <item index="98" class="java.lang.String" itemvalue="tornado" />
            <item index="99" class="java.lang.String" itemvalue="aiofiles" />
            <item index="100" class="java.lang.String" itemvalue="toml" />
            <item index="101" class="java.lang.String" itemvalue="mistune" />
            <item index="102" class="java.lang.String" itemvalue="termcolor" />
            <item index="103" class="java.lang.String" itemvalue="torch-spline-conv" />
            <item index="104" class="java.lang.String" itemvalue="pathtools" />
            <item index="105" class="java.lang.String" itemvalue="mpmath" />
            <item index="106" class="java.lang.String" itemvalue="babel" />
            <item index="107" class="java.lang.String" itemvalue="cachetools" />
            <item index="108" class="java.lang.String" itemvalue="debugpy" />
            <item index="109" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="110" class="java.lang.String" itemvalue="multidict" />
            <item index="111" class="java.lang.String" itemvalue="yarl" />
            <item index="112" class="java.lang.String" itemvalue="pytz" />
            <item index="113" class="java.lang.String" itemvalue="gmpy2" />
            <item index="114" class="java.lang.String" itemvalue="setproctitle" />
            <item index="115" class="java.lang.String" itemvalue="webencodings" />
            <item index="116" class="java.lang.String" itemvalue="traitlets" />
            <item index="117" class="java.lang.String" itemvalue="protobuf" />
            <item index="118" class="java.lang.String" itemvalue="joblib" />
            <item index="119" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="120" class="java.lang.String" itemvalue="arrow" />
            <item index="121" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="122" class="java.lang.String" itemvalue="opt-einsum" />
            <item index="123" class="java.lang.String" itemvalue="nbclient" />
            <item index="124" class="java.lang.String" itemvalue="setuptools" />
            <item index="125" class="java.lang.String" itemvalue="mkl-random" />
            <item index="126" class="java.lang.String" itemvalue="tinycss2" />
            <item index="127" class="java.lang.String" itemvalue="frozenlist" />
            <item index="128" class="java.lang.String" itemvalue="fsspec" />
            <item index="129" class="java.lang.String" itemvalue="appdirs" />
            <item index="130" class="java.lang.String" itemvalue="filelock" />
            <item index="131" class="java.lang.String" itemvalue="outdated" />
            <item index="132" class="java.lang.String" itemvalue="pyzmq" />
            <item index="133" class="java.lang.String" itemvalue="safetensors" />
            <item index="134" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="135" class="java.lang.String" itemvalue="certifi" />
            <item index="136" class="java.lang.String" itemvalue="anyio" />
            <item index="137" class="java.lang.String" itemvalue="entrypoints" />
            <item index="138" class="java.lang.String" itemvalue="pyparsing" />
            <item index="139" class="java.lang.String" itemvalue="sympy" />
            <item index="140" class="java.lang.String" itemvalue="xxhash" />
            <item index="141" class="java.lang.String" itemvalue="tokenizers" />
            <item index="142" class="java.lang.String" itemvalue="triton" />
            <item index="143" class="java.lang.String" itemvalue="sacremoses" />
            <item index="144" class="java.lang.String" itemvalue="cryptography" />
            <item index="145" class="java.lang.String" itemvalue="jinja2" />
            <item index="146" class="java.lang.String" itemvalue="backcall" />
            <item index="147" class="java.lang.String" itemvalue="ninja" />
            <item index="148" class="java.lang.String" itemvalue="widgetsnbextension" />
            <item index="149" class="java.lang.String" itemvalue="argon2-cffi-bindings" />
            <item index="150" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="151" class="java.lang.String" itemvalue="hjson" />
            <item index="152" class="java.lang.String" itemvalue="littleutils" />
            <item index="153" class="java.lang.String" itemvalue="mkl-service" />
            <item index="154" class="java.lang.String" itemvalue="numexpr" />
            <item index="155" class="java.lang.String" itemvalue="async-timeout" />
            <item index="156" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="157" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="158" class="java.lang.String" itemvalue="wcwidth" />
            <item index="159" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="160" class="java.lang.String" itemvalue="aiosqlite" />
            <item index="161" class="java.lang.String" itemvalue="lightning-utilities" />
            <item index="162" class="java.lang.String" itemvalue="wandb" />
            <item index="163" class="java.lang.String" itemvalue="sip" />
            <item index="164" class="java.lang.String" itemvalue="urllib3" />
            <item index="165" class="java.lang.String" itemvalue="torch-geometric" />
            <item index="166" class="java.lang.String" itemvalue="parso" />
            <item index="167" class="java.lang.String" itemvalue="text-unidecode" />
            <item index="168" class="java.lang.String" itemvalue="nbformat" />
            <item index="169" class="java.lang.String" itemvalue="dill" />
            <item index="170" class="java.lang.String" itemvalue="packaging" />
            <item index="171" class="java.lang.String" itemvalue="y-py" />
            <item index="172" class="java.lang.String" itemvalue="fastjsonschema" />
            <item index="173" class="java.lang.String" itemvalue="futures" />
            <item index="174" class="java.lang.String" itemvalue="argparse" />
            <item index="175" class="java.lang.String" itemvalue="chardet" />
            <item index="176" class="java.lang.String" itemvalue="typing-inspect" />
            <item index="177" class="java.lang.String" itemvalue="pillow" />
            <item index="178" class="java.lang.String" itemvalue="aiohttp" />
            <item index="179" class="java.lang.String" itemvalue="ply" />
            <item index="180" class="java.lang.String" itemvalue="aiosignal" />
            <item index="181" class="java.lang.String" itemvalue="torch_sparse" />
            <item index="182" class="java.lang.String" itemvalue="torch_scatter" />
            <item index="183" class="java.lang.String" itemvalue="opt_einsum" />
            <item index="184" class="java.lang.String" itemvalue="colorama" />
            <item index="185" class="java.lang.String" itemvalue="ortools" />
            <item index="186" class="java.lang.String" itemvalue="torchopt" />
            <item index="187" class="java.lang.String" itemvalue="beautifulsoup4" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyTypedDictInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="list.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>