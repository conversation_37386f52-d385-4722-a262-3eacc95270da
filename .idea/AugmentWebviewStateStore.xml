<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>