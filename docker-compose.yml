version: '3.8'

services:
  video_process_service:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./code:/app/code
      - ./data:/app/data
    environment:
      - STREAMLIT_SERVER_PORT=8501
      # 在这里添加您的阿里云凭证, 或者从.env文件加载
      # - OSS_ACCESS_KEY_ID=your_access_key_id
      # - OSS_ACCESS_KEY_SECRET=your_access_key_secret
      # - ALIYUN_AK_ID=your_ak_id
      # - ALIYUN_AK_SECRET=your_ak_secret
    networks:
      - video-net

  nginx:
    image: nginx:latest
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "8080:80"
    depends_on:
      - video_process_service
    networks:
      - video-net

networks:
  video-net:
    driver: bridge