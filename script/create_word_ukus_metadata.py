# -*- coding: utf-8 -*-
"""
@Time ： 2025/7/10 17:12
@Auth ： xiaolongtuan
@File ：create_word_audio_metadata.py
"""
import os
import json
from collections import defaultdict

# 定义目录路径
audio_directory = '../data/word_us_uk'

# 初始化元数据列表
metadata = defaultdict(dict)

# 遍历目录下的文件
for root, dirs, files in os.walk(audio_directory):
    for file in files:
        if file.lower().endswith(('.mp3')):
            file_path = os.path.join(root, file)

            file_url = f'http://159.138.23.13/audio/word_us_uk/{file}'
            # 从文件名中提取单词
            word_part = os.path.splitext(file)[0].split('_')[0]
            type_str = os.path.splitext(file)[0].split('_')[1]

            if word_part:
                word = word_part
            else:
                word = ""

            if type_str == 'uk':
                word_type = 'uk'
            elif type_str == 'us':
                word_type = 'us'
            else:
                raise ValueError(f'Unknown word type: {type_str}')

            # 创建元数据条目
            metadata_entry = {
                "audio_type": word_type,
                "file_url": file_url,
                "word": word
            }
            metadata[word][word_type] = metadata_entry

# 将元数据列表转换为 JSON 字符串
metadata_json = json.dumps(metadata, indent=4, ensure_ascii=False)

# 可选择将 JSON 数据保存到文件
with open('../output/word_audio_type_metadata.json', 'w', encoding='utf-8') as f:
    f.write(metadata_json)
